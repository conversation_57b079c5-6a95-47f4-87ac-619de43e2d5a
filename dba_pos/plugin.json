{"name": "dba_pos", "version": "0.0.38", "parallel": 100, "description": "plugin for dbapos upload and download", "entry": "dba_pos", "args": [{"name": "action", "is_required": true, "type": "string", "description": "download;upload"}, {"name": "endpoint", "is_required": false, "type": "string", "description": "pos gateway endpoint"}, {"name": "prefix", "is_required": false, "type": "string", "description": "upload destination path prefix"}, {"name": "target", "is_required": false, "type": "string", "description": "Uploaded local file or downloaded target directory like test_instance_upload.xb"}, {"name": "upload-mode", "is_required": false, "type": "string", "description": "file;stdin"}, {"name": "tag", "is_required": false, "type": "string", "description": "POS tag, default value: pdb-backup"}, {"name": "pns-enable", "is_required": false, "type": "string", "description": "pns enable for POS"}, {"name": "leo-enable", "is_required": false, "type": "string", "description": "leo-enable for POS"}, {"name": "host", "is_required": false, "type": "string", "description": "pos gateway config host"}, {"name": "app", "is_required": false, "type": "string", "description": "App corresponding to POS, the format is: --app=appid:appkey"}, {"name": "speed-limit", "is_required": false, "type": "string", "description": "upload speed limit, 0 is unlimit"}, {"name": "timeout", "is_required": false, "type": "string", "description": "Timeout time (unit s)"}, {"name": "part", "is_required": false, "type": "string", "description": "multipart upload part size [1MB, 1GB), if file size > 2*part, upload multipart"}, {"name": "thread", "is_required": false, "type": "string", "description": "concurrent number of multipart upload, range is [1, 32]"}, {"name": "log-level", "is_required": false, "type": "string", "description": "log level, debug,info,error..."}, {"name": "pns-cluster", "is_required": false, "type": "string", "description": "avigw-db"}, {"name": "app-service", "is_required": false, "type": "string", "description": "pdb-mysql"}, {"name": "pos-url", "is_required": false, "type": "string", "description": "pos download url"}, {"name": "target-dir", "is_required": false, "type": "string", "description": "download file target dir"}]}