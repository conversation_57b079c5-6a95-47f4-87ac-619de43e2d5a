package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
	"os"
	"strings"
	"time"
)

var defaultClient *client

func init() {
	defaultClient = newClient()
}

type client struct {
	sock       string
	jid        string
	token      string
	httpClient *http.Client
}

func newClient() *client {
	defaultTimeout := 10 * time.Second
	panda := &client{
		sock:  os.Getenv("PANDA_SOCKET"),
		jid:   os.<PERSON>env("PANDA_JID"),
		token: os.<PERSON>env("PANDA_TOKEN"),
	}
	panda.httpClient = &http.Client{
		Transport: &http.Transport{
			DialContext: func(_ context.Context, _, _ string) (net.Conn, error) {
				return net.Dial("unix", panda.sock)
			},
		},
		Timeout: defaultTimeout,
	}
	return panda
}

func ReturnPanda(v interface{}) {
	jsonStr, err := json.Marshal(v)
	if err != nil {
		defaultClient.Return([]byte(fmt.Sprintf("json Marshal error :%s", err)))
	} else {
		defaultClient.Return(jsonStr)
	}
	//os.Exit(0)
}

func Return(v []byte) error {
	return defaultClient.Return(v)
}

func (c *client) Return(v []byte) error {
	url := c.buildURL("/processes/" + c.jid + "/return")
	req, err := http.NewRequest(http.MethodPost, url, bytes.NewReader(v))
	if err != nil {
		return err
	}
	if _, err := c.do(req); err != nil {
		return err
	}
	return nil
}
func (c *client) buildURL(path string) string {
	return fmt.Sprintf("http://localhost/%s", strings.TrimLeft(path, "/"))
}

func (c *client) do(req *http.Request) ([]byte, error) {
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("X-Panda-Token", c.token)
	req.Header.Add("X-Panda-Jid", c.jid)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("error doing request: %s", err.Error())
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading body: %s", err.Error())
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("[code=%d] %s", resp.StatusCode, string(body))
	}
	return body, nil
}
