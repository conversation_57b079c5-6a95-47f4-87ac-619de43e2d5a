package main

import (
	"bufio"
	"crypto/md5"
	"encoding/base64"
	"fmt"
	"git.temu.team/temu-public-pkg-go/davinci/http/headers"
	"github.com/rs/zerolog/log"
	"io"
	"math/rand"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

const RETRY_TIME = 10

func computerMd5(fileName string) string {
	file, err := os.Open(fileName)
	defer file.Close()
	if err != nil {
		return ""
	}
	hashed := md5.New()
	if _, err := io.Copy(hashed, file); err != nil {
		fmt.Println("hash md5 fail：", err)
		return ""
	}
	md5Hash := hashed.Sum(nil)
	base64Hash := base64.StdEncoding.EncodeToString(md5Hash)
	//fmt.Println("file md5：", base64Hash)
	return base64Hash
}

func computerMd5ByReader(fileData io.Reader) string {
	hasher := md5.New()
	if _, err := io.Copy(hasher, fileData); err != nil {
		fmt.Println("hash md5 fail:", err)
		return ""
	}
	md5Hash := hasher.Sum(nil)
	base64Hash := base64.StdEncoding.EncodeToString(md5Hash)
	return base64Hash
}

func fileUpload(args *Args) (err error) {
	cli, err := getClientUploader(args)

	target, err := filepath.Abs(args.Target)
	if err != nil {
		log.Fatal().Caller().Err(err).Str("target", args.Target).Msg("target convert to absolute path failed")
	}

	err = filepath.Walk(target, func(path string, info os.FileInfo, err error) error {
		logger := log.With().Str("path", path).Logger()
		if err != nil {
			logger.Fatal().Err(err).Msg("stat file failed")
		}
		if info.IsDir() {
			// continue
			return nil
		}

		logger.Debug().Msg("start to upload")

		uploadPath := strings.TrimPrefix(path, target)
		if uploadPath == "" {
			// target is not dir
			uploadPath = info.Name()
		}
		if args.Prefix != "" {
			uploadPath = strings.TrimPrefix(args.Prefix, "/") + "/" + uploadPath
		}
		var errOnce sync.Once
		var uploadErr error
		size := info.Size()
		if size > int64(2*args.PartSize) {
			// multipart upload
			file, err := os.Open(path)
			if err != nil {
				log.Fatal().Caller().Err(err).Msg("open file failed")
				return err
			}
			defer file.Close()

			multi := cli.NewMultipartUploadObject()
			// init
			ctx, cancelFunc := ctxWithTimeout(args.Timeout)
			err = multi.Init(ctx, uploadPath, nil)
			cancelFunc()
			if err != nil {
				logger.Fatal().Err(err).Msg("multipart upload init failed")
				return err
			}

			block := make(chan struct{}, args.Concurrent)
			wg := sync.WaitGroup{}
			num := 1
			for off := int64(0); off < size; off += int64(args.PartSize) {
				if uploadErr != nil {
					return uploadErr
				}
				block <- struct{}{}
				wg.Add(1)
				go func(num int, off int64) {
					defer func() {
						<-block
						wg.Done()
					}()

					data := getBuf(int(args.PartSize))
					n, err := file.ReadAt(data, off)
					if err != nil && err != io.EOF {
						logger.Fatal().Err(err).Int64("off", off).Int("n", n).Msg("read file failed")
						errOnce.Do(func() {
							uploadErr = err
						})
					}
					if int64(n) < int64(args.PartSize) && off+int64(n) != size {
						logger.Fatal().Int("n", n).Int64("size", size).Int64("off", off).
							Msg("read part size less than expected part size")
					}

					retry := func() error {
						var singleUploadErr error
						for i := 0; i < RETRY_TIME; i++ {
							now := time.Now()
							singleUploadErr = multi.UploadPart(ctx, num, n, data[:n])
							//singleUploadErr = errors.New("mockErr")
							singleUploadCostTime := time.Now().Sub(now).Seconds()
							log.Trace().Float64("singleUploadCostTime", singleUploadCostTime).Err(singleUploadErr).
								Msg(fmt.Sprintf("index:%d upload part num:%d size:%d finish", i, num, n))
							if singleUploadErr == nil || i == 2 {
								break
							}
							rand.Intn(RETRY_TIME)
							time.Sleep(time.Duration(time.Second * int64(rand.Intn(RETRY_TIME))))
						}
						return singleUploadErr
					}
					retryErr := retry()
					putBuf(data)
					if retryErr != nil {
						errOnce.Do(func() {
							uploadErr = retryErr
						})
						log.Info().Err(retryErr).Int("partNumber", num).Int("partSize", n).Msg("multipart upload failed")

					}
					logger.Debug().Int("partNumber", num).Int("partSize", n).Msg("upload part success")
				}(num, off)

				num++
			}
			wg.Wait()
			close(block)

			ctx, cancelFunc = ctxWithTimeout(args.Timeout)
			resp, err := multi.Complete(ctx)
			if err != nil {
				logger.Fatal().Err(err).Msg("multipart upload complete failed")

				return err
			}
			cancelFunc()
			if uploadErr != nil {
				logger.Fatal().Err(uploadErr).Msg("multipart upload complete failed")
				return uploadErr
			}
			logger.Info().Str("url", resp.URL).Msg("multipart upload success")

		} else {
			// single upload
			file, err := os.Open(path)
			if err != nil {
				log.Fatal().Caller().Err(err).Msg("open file failed")
			}
			defer file.Close()
			contentMd5 := computerMd5(path)
			ctx, cancelFunc := ctxWithTimeout(args.Timeout)
			var myMap = map[string]string{}
			myMap["Content-MD5"] = contentMd5
			file.Seek(0, 0)
			resp, err := cli.NewUploadRequest().SetPath(uploadPath).SetContext(ctx).SetHeaders(myMap).SetData(file).Do()
			cancelFunc()
			if err != nil {
				logger.Fatal().Err(err).Msg("upload failed")
				return err
			}
			fmt.Println(resp.Headers.Get(headers.ContentMD5))
			logger.Info().Str("url", resp.URL).Str("md5", strings.Trim(resp.Headers.Get(headers.ContentMD5), "\"")).
				Msg("single upload success")
		}

		return nil
	})
	return err
}

func upload(args *Args) (err error) {
	if args.UploadMode == "file" {
		return fileUpload(args)
	}
	bufLen := args.PartSize
	//bufLen = 1024 * 256
	ctx, cancelFunc := ctxWithTimeout(args.Timeout)
	cli, err := getClientUploader(args)
	if err != nil {
		return err
	}
	multi, err := getMultiUploader(args, cli)
	if err != nil {
		return
	}
	reader := bufio.NewReader(os.Stdin)
	if args.UploadMode == "stdin" {
		reader = bufio.NewReader(os.Stdin)
	} else {
		pr, pw, err := os.Pipe()
		if err != nil {
			return err
		}
		oldStdin := os.Stdin
		os.Stdin = pr
		defer func() { os.Stdin = oldStdin }()

		go func() {
			defer pw.Close()
			for i := 0; i < 100; i++ {
				pw.Write([]byte("1234567890-"))
				time.Sleep(10 * time.Millisecond)
			}
		}()
		reader = bufio.NewReader(os.Stdin)
	}
	num := 1

	var wg sync.WaitGroup
	var uploadErr error
	//uploadErrChan := make(chan error, args.Concurrent*2)
	var errOnce sync.Once
	block := make(chan struct{}, args.Concurrent)
	for {
		dataBuf := getBuf(bufLen)
		dataLen, readStdInerr := io.ReadAtLeast(reader, dataBuf, bufLen)
		if readStdInerr != nil {
			if readStdInerr != io.EOF && readStdInerr != io.ErrUnexpectedEOF {
				err = readStdInerr
				break
			}

		}
		if uploadErr != nil {
			break
		}
		if dataLen == 0 {
			break
		}

		block <- struct{}{}
		log.Trace().Msg(fmt.Sprintf("curr concurr:%d", len(block)))
		wg.Add(1)
		go func(num, dataLen int, dataBuf []byte) {
			defer func() {
				<-block
				wg.Done()
			}()

			// upload part
			ctx, cancelFunc := ctxWithTimeout(args.Timeout)
			retry := func() error {
				var singleUploadErr error
				for i := 0; i < RETRY_TIME; i++ {
					now := time.Now()
					singleUploadErr = multi.UploadPart(ctx, num, dataLen, dataBuf[:dataLen])
					//singleUploadErr = errors.New("mockErr")
					singleUploadCostTime := time.Now().Sub(now).Seconds()
					log.Trace().Float64("singleUploadCostTime", singleUploadCostTime).Err(singleUploadErr).
						Msg(fmt.Sprintf("index:%d upload part num:%d size:%d finish", i, num, dataLen))
					if singleUploadErr == nil || i == 2 {
						break
					}
				}
				return singleUploadErr
			}
			retryErr := retry()
			cancelFunc()
			putBuf(dataBuf)
			if retryErr != nil {
				errOnce.Do(func() {
					uploadErr = retryErr
				})
				log.Trace().Err(retryErr).Int("partNumber", num).Int("partSize", dataLen).Msg("multipart upload failed")

			}
		}(num, dataLen, dataBuf)
		num++
	}
	wg.Wait()
	if uploadErr != nil {
		err = uploadErr
		return
	}
	ctx, cancelFunc = ctxWithTimeout(args.Timeout)
	resp, err := multi.Complete(ctx)
	cancelFunc()
	if err != nil {
		return
	}

	log.Info().Str("url", resp.URL).Msg("multipart upload success")
	return
}
