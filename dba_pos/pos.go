package main

import (
	"fmt"
	"git.temu.team/temu-public-pkg-go/goavi"
	"github.com/rs/zerolog/log"
	"path/filepath"
	"strings"
)

func getClientUploader(args *Args) (*goavi.Client, error) {
	_, cancelFunc := ctxWithTimeout(args.Timeout)
	defer cancelFunc()
	cli := goavi.NewClient().SetLeoForbidden(!args.LeoEnable).
		SetUploadSpeed(args.SpeedLimit).
		SetHost(args.Host).
		SetTimeout(args.Timeout).
		SetTag(args.Tag).SetCredentials(args.AppID(), args.AppKey()).SetPNSRsCluster(args.PNSCluster)
	if args.PNSEnable {
		cli.SetPNSEnable(args.PNSEnable)
	}
	if args.IsDev {
		cli.SetHttpsEnable(true)
	}
	if args.Endpoint != "" {
		cli.SetEndpoint(args.Endpoint)
	}
	err := cli.Init(args.AppService)
	if err != nil {
		log.Error().Err(err).Msg("init client failed")
		return nil, err
	}
	return cli, nil
}
func getMultiUploader(args *Args, cli *goavi.Client) (*goavi.MultipartUploadObject, error) {
	multi := cli.NewMultipartUploadObject()
	ctx, cancelFunc := ctxWithTimeout(args.Timeout)
	defer cancelFunc()
	fileName := filepath.Base(args.Target)
	uploadPath := strings.TrimPrefix(args.Prefix, "/") + "/" + fileName
	err := multi.Init(ctx, uploadPath, nil)
	if err != nil {
		log.Error().Err(err).Msg("init multipart upload failed")
		return nil, err
	}

	log.Info().Msg(fmt.Sprintf("upload url http://%s/%s", args.Endpoint, uploadPath))
	return multi, nil

}
