package main

import (
	"context"
	"fmt"
	"github.com/rs/zerolog/log"
	"net/url"
	"path"
	"path/filepath"
	"time"
)

func download(args *Args) error {
	u, err := url.Parse(args.PosUrl)
	if err != nil {
		return err
	}
	if len(args.Host) == 0 {
		args.Host = u.Host
	}
	if len(args.Endpoint) == 0 {
		args.Endpoint = u.Host
	}
	cli, err := getClientUploader(args)
	if err != nil {
		return err
	}
	downloadURL := args.PosUrl
	_, file_name := filepath.Split(downloadURL)
	devSubfix := ""
	if args.IsDev {
		now := time.Now()
		hour := now.Hour()
		minute := now.Minute()
		second := now.Second()
		devSubfix = fmt.Sprintf(".download.%02d:%02d:%02d", hour, minute, second)
	}

	file_name = fmt.Sprintf("%s%s", file_name, devSubfix)
	if args.TargetDir != "" {
		file_name = path.Join(args.TargetDir, file_name)
	}
	log.Info().Str("url", downloadURL).Msg("start download")
	res, err := cli.NewDownloadRequest().
		SetContext(context.Background()).
		SetURL(downloadURL).
		SetRangeSize(500 * 1024 * 1024).
		SetMultiConcurrencyFileSizeThreshold(1000 * 1024 * 1024).
		SetOutput(file_name).
		SetConcurrency(args.Concurrent).
		Do()
	log.Info().Err(err).Any("res", res).Str("url", downloadURL).Str("fileName", file_name).Msg("download finished")

	if err != nil {
		log.Fatal().Err(err).Msg("NewDownloadRequest failed")
		return err
	}
	return nil
}
