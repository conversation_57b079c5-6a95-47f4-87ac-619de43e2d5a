package main

import (
	"context"
	"errors"
	"flag"
	"fmt"
	clog "git.temu.team/temu-public-pkg-go/common-gogogo/pkg/log"
	"git.temu.team/temu-public-pkg-go/leo2-go/leo2client"
	"git.temu.team/temu-public-pkg-go/roc-golang/roc"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/sirupsen/logrus"
	"io/ioutil"
	dog "log"
	"os"
	"time"

	"git.temu.team/temu-public-pkg-go/davinci/library/monitor"
)

func init() {
	leo2client.SetAppName("pdb-mysql")
	roc.SetAppName("pdb-mysql")
	consoleWriter := zerolog.ConsoleWriter{
		Out:        os.Stdout,
		TimeFormat: "01-02 15:04:05",
		NoColor:    true,
	}
	log.Logger = zerolog.New(consoleWriter).With().Timestamp().Caller().Logger()

	// hide cat error log
	dog.SetOutput(ioutil.Discard)
	logrus.SetLevel(logrus.ErrorLevel)
	zerolog.SetGlobalLevel(zerolog.InfoLevel)
	clog.SetLogLevel("/tmp/clog.log", "ERROR")
}
func initScript() {
	flag.Usage = func() {
		_, _ = fmt.Fprintf(flag.CommandLine.Output(), "Usage of %s -endpoint [endpoint] -host [host] -app [appid:appkey]"+
			" -tag [tag] -target [target dir]\n", os.Args[0])
		flag.PrintDefaults()
	}
}

func main() {

	//target := "/data0/binlog_backup_tmp/tdb-38a9zdyu/mysql-bin.008769.tar.gz"
	//
	//err1 := filepath.Walk(target, func(path string, info os.FileInfo, err error) error {
	//	if err != nil {
	//		return err
	//	}
	//	fmt.Printf("Path: %s, Size: %d bytes\n", path, info.Size())
	//	return nil
	//})
	//
	//if err1 != nil {
	//	fmt.Printf("Error walking the path %v: %v\n", target, err1)
	//}
	startRunTime := time.Now()
	initScript()
	args := parseArgs()
	if len(args.AppService) > 0 {
		roc.SetAppName(args.AppService)
	}
	exitCode := 0
	x := roc.NewTransaction("stream_upload", "run")
	x.SetStatus(roc.STATUS_SUCCESS)
	if "debug" == args.LogLevel {
		zerolog.SetGlobalLevel(zerolog.DebugLevel)
	} else if "trace" == args.LogLevel {
		zerolog.SetGlobalLevel(zerolog.TraceLevel)
	}
	var err error
	defer func() {
		if err != nil {
			x.SetStatus("FAILURE")
			x.AddData(err.Error())
			exitCode = 1
			log.Error().Err(err).Msg("multipart upload failed")
		}
		x.Complete()
		time.Sleep(6 * time.Second)
		constTime := time.Since(startRunTime).String()
		log.Info().Msg(fmt.Sprintf("stream_upload plugin exitCode:%d err:%v end constTime:%s %s\n", exitCode, err,
			constTime, time.Now().Format("2006-01-02 15:04:05")))
		os.Exit(exitCode)
	}()
	switch args.Action {
	case "upload":
		err = upload(args)
	case "download":
		err = download(args)
	default:
		err = errors.New("unknown action")

	}
	return

}

func ctxWithTimeout(t time.Duration) (ctx context.Context, cancel context.CancelFunc) {
	return context.WithTimeout(monitor.Context(context.Background()), t)
}
