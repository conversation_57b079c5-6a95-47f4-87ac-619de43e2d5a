package main

import (
	"errors"
	"flag"
	"fmt"
	"os"
	"strings"
	"time"
)

type Args struct {
	Endpoint   string        `json:"endpoint"`
	App        string        `json:"app"`
	Host       string        `json:"host"`
	Tag        string        `json:"tag"`
	Timeout    time.Duration `json:"timeout"`
	Prefix     string        `json:"prefix"`
	Target     string        `json:"target"`
	PartSize   int           `json:"part_size"`
	Concurrent int           `json:"concurrent"`
	SpeedLimit int64         `json:"speed_limit"`
	BackupId   int64         `json:"backup_id"`
	LogLevel   string        `json:"log_level"`
	LeoEnable  bool          `json:"leo_enable"`
	PNSEnable  bool          `json:"pns_enable"`
	PNSCluster string        `json:"pns_cluster"`
	AppService string        `json:"app_service"`
	PosUrl     string        `json:"pos_url"`
	TargetDir  string        `json:"target_dir"`
	Action     string        `json:"action"`
	UploadMode string        `json:"upload_mode"`
	IsDev      bool          `json:"is_dev"`
}

func (c *Args) Check() error {
	if c.Endpoint == "" {
		return errors.New("endpoint is empty")
	}
	if len(strings.Split(c.App, ":")) != 2 {
		return fmt.Errorf("invalid app, %s", c.App)
	}
	if c.Host == "" {
		return errors.New("host is empty")
	}
	if c.Tag == "" {
		return errors.New("tag is empty")
	}
	if c.Timeout < time.Second {
		return fmt.Errorf("timeout must more than 1s, now:%v", c.Timeout)
	}
	if c.Target == "" {
		return fmt.Errorf("targe is empty")
	}
	if _, err := os.Stat(c.Target); err != nil {
		return fmt.Errorf("stat target failed, target:%s, err:%v", c.Target, err)
	}
	if c.PartSize > 1024*1024*1024 || c.PartSize < 1024*1024 {
		return fmt.Errorf("invalid partsize[%d], range:[1MB, 5GB)", c.PartSize)
	}
	if c.Concurrent < 1 || c.Concurrent > 32 {
		return fmt.Errorf("invalid concurrent number[%d], range:[1, 32]", c.Concurrent)
	}

	return nil
}

func (c *Args) AppID() string {
	return strings.Split(c.App, ":")[0]
}

func (c *Args) AppKey() string {
	return strings.Split(c.App, ":")[1]
}
func parseArgs() *Args {
	args := &Args{}
	flag.StringVar(&args.Endpoint, "endpoint", "pfs.testdev.ltd", "pos gateway endpoint")
	flag.StringVar(&args.App, "app", "90119:NOdpv7Phgss40xWs", "pos gateway config app[appid:appkey], example: 65536:abcde")
	flag.StringVar(&args.Tag, "tag", "us-pdb-mysql-publink-testing", "pos gateway config tag")
	flag.StringVar(&args.Host, "host", "pfs.testdev.ltd", "pos gateway config host")
	flag.Int64Var(&args.SpeedLimit, "speed-limit", 0, "upload speed limit, 0 is unlimit")
	flag.StringVar(&args.Prefix, "prefix", "expired1month", "upload destination path prefix")
	flag.StringVar(&args.Target, "target", "test_instance_upload.xb", "upload target, dir: upload all files; file: only upload this file")
	flag.DurationVar(&args.Timeout, "timeout", 300*time.Second, "request timeout, for the entire request lifecycle,like single upload, upload part.")
	flag.IntVar(&args.PartSize, "part", 100*1024*1024, "multipart upload part size [1MB, 1GB), if file size > 2*part, upload multipart")
	flag.IntVar(&args.Concurrent, "thread", 10, "concurrent number of multipart upload, range is [1, 32]")
	flag.StringVar(&args.LogLevel, "log-level", "debug", "log level, debug,info,error...")
	flag.BoolVar(&args.LeoEnable, "leo-enable", false, "enable leo")
	flag.BoolVar(&args.PNSEnable, "pns-enable", false, "enable pns")
	flag.StringVar(&args.PNSCluster, "pns-cluster", "avigw-db", "pns rs cluster")
	flag.StringVar(&args.AppService, "app-service", "pdb-mysql", "machine cmdb service name")
	//download
	flag.StringVar(&args.PosUrl, "pos-url", "", "pos download file url")
	flag.StringVar(&args.TargetDir, "target-dir", "/tmp/", "download file target dir, default current directory")

	flag.StringVar(&args.Action, "action", "upload", "download;upload")
	flag.StringVar(&args.UploadMode, "upload-mode", "stdin", "file;stdin")
	flag.BoolVar(&args.IsDev, "dev", false, "local dev enable")
	flag.Parse()
	return args
}
