module dba_pos

go 1.21

require (
	git.temu.team/temu-public-pkg-go/common-gogogo v0.1.11-rc1
	git.temu.team/temu-public-pkg-go/davinci v0.5.32
	git.temu.team/temu-public-pkg-go/goavi v0.0.68
	git.temu.team/temu-public-pkg-go/leo2-go v1.0.14
	git.temu.team/temu-public-pkg-go/roc-golang v0.4.0-rc26
	github.com/rs/zerolog v1.33.0
	github.com/sirupsen/logrus v1.9.3
)

require (
	git.temu.team/temu-public-pkg-go/auth-pals-go v1.0.3 // indirect
	git.temu.team/temu-public-pkg-go/booster-common-go v1.1.5 // indirect
	git.temu.team/temu-public-pkg-go/ddns-go-client v1.5.0 // indirect
	git.temu.team/temu-public-pkg-go/go-client v0.3.10 // indirect
	git.temu.team/temu-public-pkg-go/go-leo v1.2.17 // indirect
	github.com/dgrijalva/jwt-go v3.2.0+incompatible // indirect
	github.com/emirpasic/gods v1.12.0 // indirect
	github.com/go-resty/resty/v2 v2.7.0 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang-collections/collections v0.0.0-20130729185459-604e922904d3 // indirect
	github.com/golang/protobuf v1.5.2 // indirect
	github.com/google/uuid v1.6.0 // indirect
	github.com/grpc-ecosystem/go-grpc-middleware v1.0.1-0.20190118093823-f849b5445de4 // indirect
	github.com/jpillora/backoff v1.0.0 // indirect
	github.com/konsorten/go-windows-terminal-sequences v1.0.1 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/orcaman/concurrent-map v0.0.0-20210501183033-44dafcb38ecc // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/remyoudompheng/bigfft v0.0.0-20200410134404-eec4a21b6bb0 // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	go.uber.org/multierr v1.10.0 // indirect
	go.uber.org/zap v1.27.0 // indirect
	golang.org/x/net v0.21.0 // indirect
	golang.org/x/sync v0.1.0 // indirect
	golang.org/x/sys v0.27.0 // indirect
	golang.org/x/text v0.14.0 // indirect
	golang.org/x/time v0.5.0 // indirect
	google.golang.org/genproto v0.0.0-20201019141844-1ed22bb0c154 // indirect
	google.golang.org/grpc v1.29.1 // indirect
	google.golang.org/protobuf v1.27.1 // indirect
	modernc.org/mathutil v1.4.1 // indirect
)

replace (
	github.com/sirupsen/logrus => git.temu.team/temu-public-pkg-go/logrus v1.4.3-p2
	go.etcd.io/etcd => go.etcd.io/etcd v0.5.0-alpha.5.0.20201125193152-8a03d2e9614b // 8a03d2e9614b is the SHA for git tag v3.4.14
)
